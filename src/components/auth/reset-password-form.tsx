'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Icon } from '@/components/ui/icon'
import { Spinner } from '@/components/ui/spinner'
import { useAuth } from '@/hooks/useAuth'
import { isValidEmail } from '@/lib/utils'

export function ResetPasswordForm() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const { resetPassword } = useAuth()

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    if (!email) {
      setError('Por favor, insira seu email')
      return
    }

    if (!isValidEmail(email)) {
      setError('Por favor, insira um email válido')
      return
    }

    setLoading(true)

    try {
      const { error } = await resetPassword(email)
      
      if (error) {
        setError(error.message)
      } else {
        setSuccess(true)
      }
    } catch (err) {
      setError('Erro inesperado. Tente novamente.')
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-success/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Icon name="mail" size={32} className="text-success" />
          </div>
          <CardTitle className="text-2xl font-space-grotesk">
            Email enviado!
          </CardTitle>
          <CardDescription>
            Enviamos um link para redefinir sua senha para {email}. 
            Verifique sua caixa de entrada e spam.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <Link href="/login">
            <Button className="w-full">
              Voltar para login
            </Button>
          </Link>
          
          <div className="text-center text-sm text-foreground/70">
            Não recebeu o email?{' '}
            <button 
              className="text-primary hover:underline"
              onClick={() => setSuccess(false)}
            >
              Tentar novamente
            </button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <CardTitle className="text-2xl font-space-grotesk">
          Redefinir senha
        </CardTitle>
        <CardDescription>
          Digite seu email e enviaremos um link para redefinir sua senha
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {error && (
          <div className="p-3 rounded-2xl bg-destructive/10 border border-destructive/20 text-destructive text-sm">
            <Icon name="alert-circle" size={16} className="inline mr-2" />
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="text-sm font-medium mb-2 block">
              Email
            </label>
            <Input
              type="email"
              placeholder="<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              disabled={loading}
            />
          </div>

          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Enviando...
              </>
            ) : (
              'Enviar link de redefinição'
            )}
          </Button>
        </form>

        <div className="text-center text-sm text-foreground/70">
          Lembrou da senha?{' '}
          <Link href="/login" className="text-primary hover:underline">
            Fazer login
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
