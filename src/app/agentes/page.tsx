import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Icon, IconName } from '@/components/ui/icon'
import { agents } from '@/data/agents'
import Link from 'next/link'

const iconMap: Record<string, IconName> = {
  'radar': 'radar',
  'file-text': 'file-text',
  'help-circle': 'help-circle',
  'users': 'users',
  'map': 'map',
  'calendar': 'calendar',
  'bar-chart': 'bar-chart',
}

export default function AgentesPage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-space-grotesk font-bold mb-6">
            Seus 7 Agentes Estratégicos
          </h1>
          <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto">
            Cada agente é especializado em uma etapa crítica do processo de agência. 
            Clique em qualquer agente para começar a trabalhar.
          </p>
        </div>

        {/* Agents Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {agents.map((agent) => (
            <Card key={agent.id} className="group hover:shadow-xl transition-all duration-300 hover:scale-105">
              <CardHeader className="pb-4">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <Icon 
                      name={iconMap[agent.icon] || 'help-circle'} 
                      className="text-primary" 
                      size={24} 
                    />
                  </div>
                  <div>
                    <CardTitle className="text-xl group-hover:text-primary transition-colors">
                      {agent.name}
                    </CardTitle>
                  </div>
                </div>
                <CardDescription className="text-base leading-relaxed">
                  {agent.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pt-0">
                <div className="space-y-4">
                  {/* Input Types */}
                  <div>
                    <p className="text-sm font-medium text-foreground/70 mb-2">Aceita:</p>
                    <div className="flex flex-wrap gap-2">
                      {agent.input_types.map((type) => (
                        <span 
                          key={type}
                          className="px-2 py-1 bg-muted rounded-lg text-xs text-foreground/80"
                        >
                          {type === 'text' && 'Texto'}
                          {type === 'file' && 'Arquivos'}
                          {type === 'url' && 'URLs'}
                          {type === 'channel_selection' && 'Canais'}
                        </span>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <Link href={`/agentes/${agent.id}`} className="block">
                    <Button className="w-full group-hover:bg-primary/90 transition-colors">
                      Abrir agente
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-space-grotesk font-semibold mb-4">
              Pronto para começar?
            </h2>
            <p className="text-foreground/70 mb-6">
              Teste todos os agentes gratuitamente por 7 dias.
            </p>
            <Link href="/signup">
              <Button size="lg" className="text-lg px-8 py-6">
                Começar teste grátis
              </Button>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
