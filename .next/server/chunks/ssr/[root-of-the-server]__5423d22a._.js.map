{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/space_grotesk_4f9f433b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"space_grotesk_4f9f433b-module__fJfFLG__className\",\n  \"variable\": \"space_grotesk_4f9f433b-module__fJfFLG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/space_grotesk_4f9f433b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Space_Grotesk%22,%22arguments%22:[{%22variable%22:%22--font-space-grotesk%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22spaceGrotesk%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Space Grotesk', 'Space Grotesk Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,wKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,wKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,wKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_7b064e0d.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_7b064e0d-module__MOT0tq__className\",\n  \"variable\": \"inter_7b064e0d-module__MOT0tq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_7b064e0d.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport { Space_Grotesk, Inter } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst spaceGrotesk = Space_Grotesk({\n  variable: \"--font-space-grotesk\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Backroom - Seu time invisível de elite\",\n  description: \"7 IAs estratégicas que tiram você do modo reativo. Transforme sua agência com inteligência artificial especializada.\",\n  keywords: [\"IA\", \"agência\", \"marketing\", \"automação\", \"estratégia\"],\n  authors: [{ name: \"Backroom\" }],\n  creator: \"Backroom\",\n  openGraph: {\n    type: \"website\",\n    locale: \"pt_BR\",\n    url: \"https://backroom.ai\",\n    title: \"Backroom - Seu time invisível de elite\",\n    description: \"7 IAs estratégicas que tiram você do modo reativo.\",\n    siteName: \"Backroom\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"Backroom - Seu time invisível de elite\",\n    description: \"7 IAs estratégicas que tiram você do modo reativo.\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"pt-BR\" className=\"dark\">\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n        <link\n          href=\"https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap\"\n          rel=\"stylesheet\"\n        />\n      </head>\n      <body\n        className={`${spaceGrotesk.variable} ${inter.variable} font-inter antialiased`}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAgBO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;QAAC;QAAM;QAAW;QAAa;QAAa;KAAa;IACnE,SAAS;QAAC;YAAE,MAAM;QAAW;KAAE;IAC/B,SAAS;IACT,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAQ,WAAU;;0BAC3B,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;kCACpE,8OAAC;wBACC,MAAK;wBACL,KAAI;;;;;;;;;;;;0BAGR,8OAAC;gBACC,WAAW,GAAG,4JAAY,CAAC,QAAQ,CAAC,CAAC,EAAE,oJAAK,CAAC,QAAQ,CAAC,uBAAuB,CAAC;0BAE7E;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}