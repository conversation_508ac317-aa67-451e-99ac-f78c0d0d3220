export interface User {
  id: string
  email: string
  name: string
  avatar_url?: string
  role: UserRole
  company_id?: string
  created_at: string
  updated_at: string
}

export type UserRole = 'admin' | 'planner' | 'atendimento' | 'analista'

export interface Company {
  id: string
  name: string
  logo_url?: string
  size: CompanySize
  channels: string[]
  clients_count: number
  brand_colors?: {
    primary: string
    secondary: string
  }
  preferences: {
    tone: string
    default_metrics: string[]
  }
  subscription_id?: string
  subscription_status: SubscriptionStatus
  trial_ends_at?: string
  created_at: string
  updated_at: string
}

export type CompanySize = 'small' | 'medium' | 'large' | 'enterprise'
export type SubscriptionStatus = 'trial' | 'active' | 'past_due' | 'canceled' | 'incomplete'

export interface Campaign {
  id: string
  name: string
  company_id: string
  client_name: string
  status: CampaignStatus
  created_by: string
  agents_used: AgentType[]
  results: AgentResult[]
  created_at: string
  updated_at: string
}

export type CampaignStatus = 'draft' | 'active' | 'completed' | 'archived'

export interface AgentResult {
  id: string
  campaign_id: string
  agent_type: AgentType
  input_data: any
  output_data: any
  status: AgentResultStatus
  created_at: string
  updated_at: string
}

export type AgentResultStatus = 'pending' | 'processing' | 'completed' | 'error'

export type AgentType = 
  | 'intake-radar'
  | 'briefing-builder'
  | 'perguntas-criticas'
  | 'persona-segmentos'
  | 'jornada-mapper'
  | 'media-planner'
  | 'performance-analyst'

export interface Agent {
  id: AgentType
  name: string
  description: string
  icon: string
  prompt_template: string
  input_types: InputType[]
  output_format: string
}

export type InputType = 'text' | 'file' | 'url' | 'channel_selection'

export interface AgentInput {
  type: InputType
  value: string | File | string[]
  metadata?: Record<string, any>
}

export interface Subscription {
  id: string
  company_id: string
  stripe_subscription_id: string
  status: SubscriptionStatus
  plan: SubscriptionPlan
  current_period_start: string
  current_period_end: string
  cancel_at_period_end: boolean
  created_at: string
  updated_at: string
}

export interface SubscriptionPlan {
  id: string
  name: string
  price: number
  currency: string
  interval: 'month' | 'year'
  max_users: number
  features: string[]
}

export interface OnboardingData {
  step: number
  company_info?: {
    name: string
    size: CompanySize
    channels: string[]
    clients_count: number
  }
  branding?: {
    logo_url?: string
    primary_color: string
    secondary_color: string
  }
  preferences?: {
    tone: string
    default_metrics: string[]
  }
}

export interface ExportOptions {
  format: 'pdf' | 'docx' | 'csv'
  include_metadata: boolean
  template?: string
}
