import { Agent } from '@/types'

export const agents: Agent[] = [
  {
    id: 'intake-radar',
    name: 'Intake Radar',
    description: 'Transforma pedidos soltos em sumário acionável.',
    icon: 'radar',
    prompt_template: `<PERSON><PERSON> todo o conteúdo fornecido (e-mails/WhatsApp/call notes). Entregue: [1] Objetivos [2] Restrições [3] Riscos [4] Dúvidas críticas [5] Próximos passos [6] Rascunho de e-mail de confirmação ao cliente. Use bullets, seja específico.`,
    input_types: ['text', 'file', 'url'],
    output_format: 'structured_summary'
  },
  {
    id: 'briefing-builder',
    name: 'Briefing Builder',
    description: 'Gera o briefing v1 completo, pronto para revisão.',
    icon: 'file-text',
    prompt_template: `Com base no Intake, gere Briefing v1 com seções: Contexto, Objetivo, Público, Mensagem, Canais, Entregáveis, Prazos, Aprovações, Métricas, Assunções, Pendências. Aplique checklist de qualidade no final.`,
    input_types: ['text', 'file'],
    output_format: 'briefing_document'
  },
  {
    id: 'perguntas-criticas',
    name: 'Perguntas Críticas',
    description: 'Lista o que precisa voltar para o cliente, por prioridade.',
    icon: 'help-circle',
    prompt_template: `Liste perguntas ao cliente priorizadas por risco/impacto. Evite redundância. Escreva e-mail de follow-up com prazos e responsáveis.`,
    input_types: ['text', 'file'],
    output_format: 'prioritized_questions'
  },
  {
    id: 'persona-segmentos',
    name: 'Persona & Segmentos',
    description: 'Cria 2–3 personas acionáveis com canais e mensagens.',
    icon: 'users',
    prompt_template: `Crie 2–3 personas com: dores, ganhos, objeções, canais, mensagens-chave, exemplos de copy. Justifique brevemente cada decisão.`,
    input_types: ['text', 'file', 'channel_selection'],
    output_format: 'persona_profiles'
  },
  {
    id: 'jornada-mapper',
    name: 'Jornada Mapper',
    description: 'Mapa por estágio: o que dizer, onde e como medir.',
    icon: 'map',
    prompt_template: `Monte jornada Aware→Consider→Convert→Retain ligando barreiras, gatilhos, mensagens, canais e métricas por estágio.`,
    input_types: ['text', 'file', 'channel_selection'],
    output_format: 'journey_map'
  },
  {
    id: 'media-planner',
    name: 'Media Planner',
    description: 'Divide orçamento e cronograma com justificativas e "e se".',
    icon: 'calendar',
    prompt_template: `Sugira distribuição inicial de orçamento por canal (inclua suposições e riscos), cronograma de 8 semanas e 3 cenários "e se".`,
    input_types: ['text', 'file', 'channel_selection'],
    output_format: 'media_plan'
  },
  {
    id: 'performance-analyst',
    name: 'Performance Analyst',
    description: 'KPIs, UTMs e relatório claro: o que, por quê, próximo passo.',
    icon: 'bar-chart',
    prompt_template: `Sugira KPIs por objetivo/canal, padronize UTMs (modelo), valide consistência e gere relatório executivo (o que aconteceu, porquê, próximos passos).`,
    input_types: ['text', 'file', 'channel_selection'],
    output_format: 'performance_report'
  }
]

export const getAgentById = (id: string) => {
  return agents.find(agent => agent.id === id)
}

export const getAgentsByType = (inputType: string) => {
  return agents.filter(agent => agent.input_types.includes(inputType as any))
}
