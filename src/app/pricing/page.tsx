import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Icon } from '@/components/ui/icon'
import Link from 'next/link'

const plans = [
  {
    name: 'Starter',
    price: 499,
    period: 'mês',
    description: 'Perfeito para agências pequenas',
    maxUsers: 5,
    features: [
      'Todos os 7 agentes de IA',
      'Até 5 usuários',
      'Histórico de projetos',
      'Exportação PDF/Docx/CSV',
      'Suporte por email',
      'Integrações básicas'
    ],
    popular: false,
    cta: 'Começar teste grátis'
  },
  {
    name: 'Professional',
    price: 1290,
    period: 'mês',
    description: 'Para agências em crescimento',
    maxUsers: 20,
    features: [
      'Todos os 7 agentes de IA',
      'Até 20 usuários',
      'Histórico ilimitado',
      'Exportação avançada',
      'Suporte prioritário',
      'Todas as integrações',
      'API personalizada',
      'Relatórios avançados'
    ],
    popular: true,
    cta: 'Começar teste grátis'
  },
  {
    name: 'Enterprise',
    price: null,
    period: 'sob consulta',
    description: 'Para grandes agências',
    maxUsers: null,
    features: [
      'Todos os 7 agentes de IA',
      'Usuários ilimitados',
      'Implementação dedicada',
      'Treinamento personalizado',
      'Suporte 24/7',
      'SLA garantido',
      'Integrações customizadas',
      'Gerente de conta dedicado'
    ],
    popular: false,
    cta: 'Falar com vendas'
  }
]

export default function PricingPage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-space-grotesk font-bold mb-6">
            Preços simples e transparentes
          </h1>
          <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto mb-8">
            Escolha o plano ideal para sua agência. Todos os planos incluem 7 dias de teste grátis.
          </p>
          
          <div className="inline-flex items-center bg-muted rounded-2xl p-1">
            <Badge variant="success" className="mr-2">
              <Icon name="check" size={14} className="mr-1" />
              7 dias grátis
            </Badge>
            <span className="text-sm text-foreground/70">
              Sem compromisso • Cancele quando quiser
            </span>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto mb-16">
          {plans.map((plan) => (
            <Card 
              key={plan.name} 
              className={`relative ${plan.popular ? 'ring-2 ring-primary shadow-xl scale-105' : ''}`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <Badge className="px-4 py-1">Mais popular</Badge>
                </div>
              )}
              
              <CardHeader className="text-center pb-8">
                <CardTitle className="text-2xl font-space-grotesk">
                  {plan.name}
                </CardTitle>
                <CardDescription className="text-base">
                  {plan.description}
                </CardDescription>
                
                <div className="mt-6">
                  {plan.price ? (
                    <div className="flex items-baseline justify-center">
                      <span className="text-4xl font-bold">R$ {plan.price}</span>
                      <span className="text-foreground/70 ml-2">/{plan.period}</span>
                    </div>
                  ) : (
                    <div className="text-2xl font-semibold text-foreground/70">
                      {plan.period}
                    </div>
                  )}
                </div>
              </CardHeader>
              
              <CardContent className="space-y-6">
                {/* Features */}
                <ul className="space-y-3">
                  {plan.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <Icon name="check" size={16} className="text-primary mr-3 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* CTA */}
                <div className="pt-4">
                  {plan.name === 'Enterprise' ? (
                    <Link href="/contato">
                      <Button variant="outline" className="w-full">
                        {plan.cta}
                      </Button>
                    </Link>
                  ) : (
                    <Link href="/signup">
                      <Button 
                        className={`w-full ${plan.popular ? 'bg-primary hover:bg-primary/90' : ''}`}
                        variant={plan.popular ? 'default' : 'outline'}
                      >
                        {plan.cta}
                      </Button>
                    </Link>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl font-space-grotesk font-bold text-center mb-12">
            Perguntas frequentes
          </h2>
          
          <div className="space-y-6">
            {[
              {
                question: 'Como funciona o teste grátis?',
                answer: 'Você tem 7 dias para testar todos os recursos sem limitações. Não cobramos nada durante o período de teste.'
              },
              {
                question: 'Posso cancelar a qualquer momento?',
                answer: 'Sim, você pode cancelar sua assinatura a qualquer momento. Não há multas ou taxas de cancelamento.'
              },
              {
                question: 'Os dados ficam seguros?',
                answer: 'Sim, todos os dados são criptografados e armazenados com segurança. Seguimos as melhores práticas de segurança.'
              },
              {
                question: 'Posso mudar de plano depois?',
                answer: 'Claro! Você pode fazer upgrade ou downgrade do seu plano a qualquer momento.'
              }
            ].map((faq, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-foreground/70">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto">
            <h2 className="text-2xl font-space-grotesk font-semibold mb-4">
              Pronto para transformar sua agência?
            </h2>
            <p className="text-foreground/70 mb-6">
              Junte-se a centenas de agências que já usam o Backroom.
            </p>
            <Link href="/signup">
              <Button size="lg" className="text-lg px-8 py-6">
                Começar teste grátis
              </Button>
            </Link>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
