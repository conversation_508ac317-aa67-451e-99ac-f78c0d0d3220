{"mappings": ";;;;;;;;;;AAAA,0HAA0H;;;AAKnH,MAAM;IAmEX,IAAW,cAAoB;QAC7B,OAAO,IAAI,CAAC,YAAY;IAC1B;IAEA,IAAW,YAAY,IAAU,EAAE;QACjC,IAAI,CAAC,CAAA,GAAA,sCAAW,EAAE,IAAI,CAAC,IAAI,EAAE,OAC3B,MAAM,IAAI,MACR;QAIJ,MAAM,UAAwB,EAAE;QAChC,IAAI,UAAmC;QACvC,IAAI,2BAA2B;QAE/B,IAAI,CAAC,YAAY,GAAG;QAEpB,MAAO,WAAW,YAAY,IAAI,CAAC,IAAI,CACrC,IAAI,QAAQ,QAAQ,KAAK,KAAK,sBAAsB,EAAE;YACpD,MAAM,aAAa;YAEnB,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;gBAAC,YAAY,IAAI,CAAC,WAAW;YAAA;YAG/B,QAAQ,IAAI,CAAC;YAEb,OAAO,WAAW,GAAG;YAErB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YAExB,UAAU,2BAA2B,WAAW,IAAI;QACtD,OACE,UAAU,QAAQ,UAAU;QAIhC,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,UAAU,EACf;YAAC,YAAY,IAAI,CAAC,WAAW;QAAA;QAG/B,QAAQ,IAAI,CAAC;QAEb,OAAO,WAAW,GAAG;QAErB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QAExB,IAAI,CAAC,YAAY,GAAG;IACtB;IAEA,IAAW,MAAgB;QACzB,OAAO,IAAI,CAAC,IAAI;IAClB;IAEO,aAA0B;QAC/B,IAAI,cAAc,IAAI,CAAC,WAAW;QAClC,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,CAAC,CAAA,GAAA,sCAAW,EAAE,aAAa,UAAU;YACvC,IAAI,CAAC,WAAW,GAAG;YACnB,OAAO;QACT;QACA,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;QAErB,OAAO;IACT;IAEO,YAAyB;QAC9B,IAAI,SAAS,IAAI,CAAC,YAAY,CAAC,EAAE;QACjC,IAAI,UAAU,OAAO,SAAS;QAC9B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;QAErB,OAAO;IACT;IAEO,WAAwB;QAC7B,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,QAAQ;QAE9C,IAAI,UAAU;YACZ,MAAM,aAAa,AAAC,SAAqB,UAAU;YAEnD,IAAI,YAAY;oBAKH;gBAJX,IAAI;gBAEJ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,aAAa,IAAI,CAAC,MAAM,CAAC;qBACpB,KAAI,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,UAAU,EAChC,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAGtC,IAAI,eAAe,WAAW,aAAa,EAAE;oBAC3C,IAAI,CAAC,WAAW,GAAG;oBACnB,OAAO;gBACT;gBAEA,4DAA4D;gBAC5D,qBAAqB;gBACrB,IAAI,UAAU,IAAI,CAAC,QAAQ;gBAC3B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT;YAEA,IAAI,UACF,IAAI,CAAC,WAAW,GAAG;YAErB,OAAO;QACT,OAAO;YACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;gBAEvB,IAAI,UAAU,IAAI,CAAC,QAAQ;gBAC3B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT,OACE,OAAO;QAEX;IACF;IAEO,eAA4B;QACjC,MAAM,gBAAgB,IAAI,CAAC,YAAY,CAAC,EAAE;QAE1C,IAAI,cAAc,WAAW,KAAK,cAAc,IAAI,EAAE;YACpD,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,gBAAgB;gBAC1C,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAE3B,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;oBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;oBACvB,IAAI,UAAU,IAAI,CAAC,YAAY;oBAC/B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;oBAErB,OAAO;gBACT,OACE,OAAO;YAEX;YAEA,OAAO;QACT;QAEA,MAAM,eAAe,cAAc,YAAY;QAE/C,IAAI,cAAc;YAChB,MAAM,aAAa,AAAC,aAAyB,UAAU;YAEvD,IAAI,YAAY;oBAKH;gBAJX,IAAI;gBAEJ,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,aAAa,IAAI,CAAC,MAAM,CAAC;qBACpB,KAAI,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,UAAU,EAChC,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;gBAGtC,IAAI,eAAe,WAAW,aAAa,EAAE;oBAC3C,IAAI,cACF,IAAI,CAAC,WAAW,GAAG;oBAErB,OAAO;gBACT;gBAEA,4DAA4D;gBAC5D,qBAAqB;gBACrB,IAAI,UAAU,IAAI,CAAC,SAAS;gBAC5B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT;YAEA,IAAI,cACF,IAAI,CAAC,WAAW,GAAG;YAErB,OAAO;QACT,OAAO;YACL,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,GAAG;gBAChC,IAAI,CAAC,YAAY,CAAC,KAAK;gBAEvB,IAAI,UAAU,IAAI,CAAC,YAAY;gBAC/B,IAAI,SACF,IAAI,CAAC,WAAW,GAAG;gBAErB,OAAO;YACT,OACE,OAAO;QAEX;IACF;IAEE;;KAEC,GACH,AAAO,cAA2B;QAChC,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IAEE;;KAEC,GACH,AAAO,kBAA+B;QACpC,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IAEE;;KAEC,GACH,AAAO,aAA0B;QAC/B,iBAAiB;QACjB,kDAAkD;QAClD,IAAI;QAEJ,OAAO;IACT;IA/RA,YACI,GAAa,EACb,IAAU,EACV,UAAmB,EACnB,MAA0B,CAC1B;aATI,eAAkC,EAAE;aAEpC,iBAAkC,IAAI;aA+BtC,cAAc,CAAC;YACrB,IAAI,KAAK,QAAQ,KAAK,KAAK,YAAY,EAAE;gBACvC,MAAM,aAAa,AAAC,KAAiB,UAAU;gBAE/C,IAAI,YAAY;oBACd,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;wBAAC,YAAY,IAAI,CAAC,WAAW;oBAAA;oBAG/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;oBAE1B,OAAO,WAAW,aAAa;gBACjC,OAAO;wBAGM;oBAFX,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,YACzB,OAAO,IAAI,CAAC,MAAM,CAAC;yBACd,KAAI,eAAA,IAAI,CAAC,MAAM,cAAX,mCAAA,aAAa,UAAU,EAChC,OAAO,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC;yBACzB,IAAI,IAAI,CAAC,MAAM,KAAK,MACzB,OAAO,WAAW,aAAa;gBAEnC;YACF;YAEA,OAAO,WAAW,WAAW;QAC/B;QAjDE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG,mBAAA,oBAAA,SAAU;QACxB,IAAI,CAAC,UAAU,GAAG,uBAAA,wBAAA,aAAc,WAAW,QAAQ;QACnD,IAAI,CAAC,YAAY,GAAG;QAEpB,IAAI,CAAC,YAAY,CAAC,OAAO,CACvB,IAAI,gBAAgB,CAAC,MAAM,YAAY,IAAI,CAAC,WAAW;QAGzD,MAAM,aAAa,AAAC,KAAiB,UAAU;QAE/C,IAAI,YAAY;YACd,MAAM,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB,CACvC,YACA,IAAI,CAAC,UAAU,EACf;gBAAC,YAAY,IAAI,CAAC,WAAW;YAAA;YAG/B,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;QAC5B;IACF;AAqQF;AAKO,SAAS,0CACZ,GAAa,EACb,IAAU,EACV,UAAmB,EACnB,MAA0B;IAE5B,IAAI,CAAA,GAAA,kCAAQ,KACV,OAAO,IAAI,0CAAiB,KAAK,MAAM,YAAY;IAErD,OAAO,IAAI,gBAAgB,CAAC,MAAM,YAAY;AAChD", "sources": ["packages/@react-aria/utils/src/shadowdom/ShadowTreeWalker.ts"], "sourcesContent": ["// https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/ShadowTreeWalker.ts\n\nimport {nodeContains} from './DOMFunctions';\nimport {shadowDOM} from '@react-stately/flags';\n\nexport class ShadowTreeWalker implements TreeWalker {\n  public readonly filter: NodeFilter | null;\n  public readonly root: Node;\n  public readonly whatToShow: number;\n\n  private _doc: Document;\n  private _walkerStack: Array<TreeWalker> = [];\n  private _currentNode: Node;\n  private _currentSetFor: Set<TreeWalker> = new Set();\n\n  constructor(\n      doc: Document,\n      root: Node,\n      whatToShow?: number,\n      filter?: NodeFilter | null\n    ) {\n    this._doc = doc;\n    this.root = root;\n    this.filter = filter ?? null;\n    this.whatToShow = whatToShow ?? NodeFilter.SHOW_ALL;\n    this._currentNode = root;\n\n    this._walkerStack.unshift(\n      doc.createTreeWalker(root, whatToShow, this._acceptNode)\n    );\n\n    const shadowRoot = (root as Element).shadowRoot;\n\n    if (shadowRoot) {\n      const walker = this._doc.createTreeWalker(\n        shadowRoot,\n        this.whatToShow,\n        {acceptNode: this._acceptNode}\n      );\n\n      this._walkerStack.unshift(walker);\n    }\n  }\n\n  private _acceptNode = (node: Node): number => {\n    if (node.nodeType === Node.ELEMENT_NODE) {\n      const shadowRoot = (node as Element).shadowRoot;\n\n      if (shadowRoot) {\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        this._walkerStack.unshift(walker);\n\n        return NodeFilter.FILTER_ACCEPT;\n      } else {\n        if (typeof this.filter === 'function') {\n          return this.filter(node);\n        } else if (this.filter?.acceptNode) {\n          return this.filter.acceptNode(node);\n        } else if (this.filter === null) {\n          return NodeFilter.FILTER_ACCEPT;\n        }\n      }\n    }\n\n    return NodeFilter.FILTER_SKIP;\n  };\n\n  public get currentNode(): Node {\n    return this._currentNode;\n  }\n\n  public set currentNode(node: Node) {\n    if (!nodeContains(this.root, node)) {\n      throw new Error(\n        'Cannot set currentNode to a node that is not contained by the root node.'\n      );\n    }\n\n    const walkers: TreeWalker[] = [];\n    let curNode: Node | null | undefined = node;\n    let currentWalkerCurrentNode = node;\n\n    this._currentNode = node;\n\n    while (curNode && curNode !== this.root) {\n      if (curNode.nodeType === Node.DOCUMENT_FRAGMENT_NODE) {\n        const shadowRoot = curNode as ShadowRoot;\n\n        const walker = this._doc.createTreeWalker(\n          shadowRoot,\n          this.whatToShow,\n          {acceptNode: this._acceptNode}\n        );\n\n        walkers.push(walker);\n\n        walker.currentNode = currentWalkerCurrentNode;\n\n        this._currentSetFor.add(walker);\n\n        curNode = currentWalkerCurrentNode = shadowRoot.host;\n      } else {\n        curNode = curNode.parentNode;\n      }\n    }\n\n    const walker = this._doc.createTreeWalker(\n      this.root,\n      this.whatToShow,\n      {acceptNode: this._acceptNode}\n    );\n\n    walkers.push(walker);\n\n    walker.currentNode = currentWalkerCurrentNode;\n\n    this._currentSetFor.add(walker);\n\n    this._walkerStack = walkers;\n  }\n\n  public get doc(): Document {\n    return this._doc;\n  }\n\n  public firstChild(): Node | null {\n    let currentNode = this.currentNode;\n    let newNode = this.nextNode();\n    if (!nodeContains(currentNode, newNode)) {\n      this.currentNode = currentNode;\n      return null;\n    }\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public lastChild(): Node | null {\n    let walker = this._walkerStack[0];\n    let newNode = walker.lastChild();\n    if (newNode) {\n      this.currentNode = newNode;\n    }\n    return newNode;\n  }\n\n  public nextNode(): Node | null {\n    const nextNode = this._walkerStack[0].nextNode();\n\n    if (nextNode) {\n      const shadowRoot = (nextNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(nextNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(nextNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          this.currentNode = nextNode;\n          return nextNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (nextNode) {\n        this.currentNode = nextNode;\n      }\n      return nextNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.nextNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n  public previousNode(): Node | null {\n    const currentWalker = this._walkerStack[0];\n\n    if (currentWalker.currentNode === currentWalker.root) {\n      if (this._currentSetFor.has(currentWalker)) {\n        this._currentSetFor.delete(currentWalker);\n\n        if (this._walkerStack.length > 1) {\n          this._walkerStack.shift();\n          let newNode = this.previousNode();\n          if (newNode) {\n            this.currentNode = newNode;\n          }\n          return newNode;\n        } else {\n          return null;\n        }\n      }\n\n      return null;\n    }\n\n    const previousNode = currentWalker.previousNode();\n\n    if (previousNode) {\n      const shadowRoot = (previousNode as Element).shadowRoot;\n\n      if (shadowRoot) {\n        let nodeResult: number | undefined;\n\n        if (typeof this.filter === 'function') {\n          nodeResult = this.filter(previousNode);\n        } else if (this.filter?.acceptNode) {\n          nodeResult = this.filter.acceptNode(previousNode);\n        }\n\n        if (nodeResult === NodeFilter.FILTER_ACCEPT) {\n          if (previousNode) {\n            this.currentNode = previousNode;\n          }\n          return previousNode;\n        }\n\n        // _acceptNode should have added new walker for this shadow,\n        // go in recursively.\n        let newNode = this.lastChild();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      }\n\n      if (previousNode) {\n        this.currentNode = previousNode;\n      }\n      return previousNode;\n    } else {\n      if (this._walkerStack.length > 1) {\n        this._walkerStack.shift();\n\n        let newNode = this.previousNode();\n        if (newNode) {\n          this.currentNode = newNode;\n        }\n        return newNode;\n      } else {\n        return null;\n      }\n    }\n  }\n\n    /**\n     * @deprecated\n     */\n  public nextSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public previousSibling(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n\n    /**\n     * @deprecated\n     */\n  public parentNode(): Node | null {\n    // if (__DEV__) {\n    //     throw new Error(\"Method not implemented.\");\n    // }\n\n    return null;\n  }\n}\n\n/**\n * ShadowDOM safe version of document.createTreeWalker.\n */\nexport function createShadowTreeWalker(\n    doc: Document,\n    root: Node,\n    whatToShow?: number,\n    filter?: NodeFilter | null\n): TreeWalker {\n  if (shadowDOM()) {\n    return new ShadowTreeWalker(doc, root, whatToShow, filter);\n  }\n  return doc.createTreeWalker(root, whatToShow, filter);\n}\n"], "names": [], "version": 3, "file": "ShadowTreeWalker.main.js.map"}