'use client'

import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Icon } from '@/components/ui/icon'
import { useState } from 'react'

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
            <span className="text-background font-bold text-sm">B</span>
          </div>
          <span className="font-space-grotesk font-bold text-xl">Backroom</span>
        </Link>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-8">
          <Link 
            href="/agentes" 
            className="text-foreground/70 hover:text-foreground transition-colors"
          >
            Agentes
          </Link>
          <Link 
            href="/pricing" 
            className="text-foreground/70 hover:text-foreground transition-colors"
          >
            Preços
          </Link>
          <Link 
            href="/suporte" 
            className="text-foreground/70 hover:text-foreground transition-colors"
          >
            Suporte
          </Link>
        </nav>

        {/* Desktop CTA */}
        <div className="hidden md:flex items-center space-x-4">
          <Link href="/login">
            <Button variant="ghost">Entrar</Button>
          </Link>
          <Link href="/signup">
            <Button>Começar teste grátis</Button>
          </Link>
        </div>

        {/* Mobile Menu Button */}
        <Button
          variant="ghost"
          size="icon"
          className="md:hidden"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          <Icon name={isMenuOpen ? 'x' : 'menu'} />
        </Button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden border-t border-border bg-background">
          <nav className="container mx-auto px-4 py-4 space-y-4">
            <Link 
              href="/agentes" 
              className="block text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Agentes
            </Link>
            <Link 
              href="/pricing" 
              className="block text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Preços
            </Link>
            <Link 
              href="/suporte" 
              className="block text-foreground/70 hover:text-foreground transition-colors"
              onClick={() => setIsMenuOpen(false)}
            >
              Suporte
            </Link>
            <div className="pt-4 space-y-2">
              <Link href="/login" className="block">
                <Button variant="ghost" className="w-full justify-start">
                  Entrar
                </Button>
              </Link>
              <Link href="/signup" className="block">
                <Button className="w-full">Começar teste grátis</Button>
              </Link>
            </div>
          </nav>
        </div>
      )}
    </header>
  )
}
