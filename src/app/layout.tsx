import type { Metadata } from "next";
import { Space_Grotesk, Inter } from "next/font/google";
import "./globals.css";

const spaceGrotesk = Space_Grotesk({
  variable: "--font-space-grotesk",
  subsets: ["latin"],
  display: "swap",
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "Backroom - Seu time invisível de elite",
  description: "7 IAs estratégicas que tiram você do modo reativo. Transforme sua agência com inteligência artificial especializada.",
  keywords: ["IA", "agência", "marketing", "automação", "estratégia"],
  authors: [{ name: "Backroom" }],
  creator: "Backroom",
  openGraph: {
    type: "website",
    locale: "pt_BR",
    url: "https://backroom.ai",
    title: "Backroom - Seu time invisível de elite",
    description: "7 IAs estratégicas que tiram você do modo reativo.",
    siteName: "Backroom",
  },
  twitter: {
    card: "summary_large_image",
    title: "Backroom - Seu time invisível de elite",
    description: "7 IAs estratégicas que tiram você do modo reativo.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR" className="dark">
      <body
        className={`${spaceGrotesk.variable} ${inter.variable} font-inter antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
