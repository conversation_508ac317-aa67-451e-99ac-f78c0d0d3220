import { 
  Radar,
  FileText,
  HelpCircle,
  Users,
  Map,
  Calendar,
  BarChart,
  Upload,
  Link,
  Copy,
  Download,
  Mail,
  Search,
  Plus,
  Settings,
  User,
  LogOut,
  Menu,
  X,
  ChevronDown,
  ChevronRight,
  Check,
  AlertCircle,
  Info,
  type LucideIcon
} from 'lucide-react'

export type IconName = 
  | 'radar'
  | 'file-text'
  | 'help-circle'
  | 'users'
  | 'map'
  | 'calendar'
  | 'bar-chart'
  | 'upload'
  | 'link'
  | 'copy'
  | 'download'
  | 'mail'
  | 'search'
  | 'plus'
  | 'settings'
  | 'user'
  | 'log-out'
  | 'menu'
  | 'x'
  | 'chevron-down'
  | 'chevron-right'
  | 'check'
  | 'alert-circle'
  | 'info'

const iconMap: Record<IconName, LucideIcon> = {
  'radar': Radar,
  'file-text': FileText,
  'help-circle': HelpCircle,
  'users': Users,
  'map': Map,
  'calendar': Calendar,
  'bar-chart': BarChart,
  'upload': Upload,
  'link': Link,
  'copy': Copy,
  'download': Download,
  'mail': Mail,
  'search': Search,
  'plus': Plus,
  'settings': Settings,
  'user': User,
  'log-out': LogOut,
  'menu': Menu,
  'x': X,
  'chevron-down': ChevronDown,
  'chevron-right': ChevronRight,
  'check': Check,
  'alert-circle': AlertCircle,
  'info': Info,
}

interface IconProps {
  name: IconName
  className?: string
  size?: number
}

export const Icon = ({ name, className, size = 20 }: IconProps) => {
  const IconComponent = iconMap[name]
  
  if (!IconComponent) {
    console.warn(`Icon "${name}" not found`)
    return null
  }

  return <IconComponent className={className} size={size} />
}

export { iconMap }
