@import "tailwindcss";

:root {
  --background: #111317;
  --foreground: #E6E6F0;
  --primary: #7F61E6;
  --secondary: #1A1D23;
  --muted: #2A2D35;
  --border: #3A3D45;
  --accent: #7F61E6;
  --destructive: #EF4444;
  --warning: #F59E0B;
  --success: #10B981;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', sans-serif;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  scroll-behavior: smooth;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: 'Space Grotesk', sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

a {
  color: inherit;
  text-decoration: none;
}

button {
  font-family: inherit;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--muted);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border);
}

/* Selection */
::selection {
  background: var(--primary);
  color: var(--background);
}

/* Focus styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Loading skeleton animation */
@keyframes skeleton {
  0% {
    background-position: -200px 0;
  }

  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  background: linear-gradient(90deg, var(--muted) 25%, var(--border) 50%, var(--muted) 75%);
  background-size: 200px 100%;
  animation: skeleton 1.5s infinite linear;
}