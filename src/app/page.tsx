import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Head<PERSON> } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'

export default function Home() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-5xl md:text-7xl font-space-grotesk font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Backroom.
          </h1>
          <h2 className="text-2xl md:text-3xl font-space-grotesk font-semibold mb-4 text-foreground/90">
            Seu time invisível de elite.
          </h2>
          <p className="text-lg md:text-xl text-foreground/70 mb-8 max-w-2xl mx-auto">
            7 IAs estratégicas que tiram você do modo reativo.
          </p>

          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Button size="lg" className="text-lg px-8 py-6">
              Começar teste grátis
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8 py-6">
              Ver agentes
            </Button>
          </div>
        </div>
      </section>

      {/* Agents Preview */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-12">
          <h3 className="text-3xl font-space-grotesk font-semibold mb-4">
            Seus 7 Agentes Estratégicos
          </h3>
          <p className="text-foreground/70 max-w-2xl mx-auto">
            Cada agente é especializado em uma etapa crítica do processo de agência.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {[
            { name: 'Intake Radar', desc: 'Transforma pedidos soltos em sumário acionável.' },
            { name: 'Briefing Builder', desc: 'Gera o briefing v1 completo, pronto para revisão.' },
            { name: 'Perguntas Críticas', desc: 'Lista o que precisa voltar para o cliente, por prioridade.' },
            { name: 'Persona & Segmentos', desc: 'Cria 2–3 personas acionáveis com canais e mensagens.' },
            { name: 'Jornada Mapper', desc: 'Mapa por estágio: o que dizer, onde e como medir.' },
            { name: 'Media Planner', desc: 'Divide orçamento e cronograma com justificativas e "e se".' },
            { name: 'Performance Analyst', desc: 'KPIs, UTMs e relatório claro: o que, por quê, próximo passo.' },
          ].map((agent, index) => (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-lg">{agent.name}</CardTitle>
                <CardDescription>{agent.desc}</CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Abrir agente
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-2xl mx-auto">
          <h3 className="text-3xl font-space-grotesk font-semibold mb-4">
            Pronto para sair do modo reativo?
          </h3>
          <p className="text-foreground/70 mb-8">
            Teste grátis por 7 dias. Sem compromisso.
          </p>
          <Button size="lg" className="text-lg px-8 py-6">
            Começar teste grátis
          </Button>
        </div>
      </section>

      <Footer />
    </div>
  );
}
