import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Header } from '@/components/layout/header'
import { Footer } from '@/components/layout/footer'
import { Icon } from '@/components/ui/icon'

export default function SuportePage() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />
      
      <main className="container mx-auto px-4 py-12">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-6xl font-space-grotesk font-bold mb-6">
            Como podemos ajudar?
          </h1>
          <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto">
            Encontre respostas rápidas ou entre em contato conosco.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-6xl mx-auto">
          {/* FAQ Section */}
          <div>
            <h2 className="text-2xl font-space-grotesk font-bold mb-8">
              Perguntas Frequentes
            </h2>
            
            <div className="space-y-4">
              {[
                {
                  question: 'Como começar a usar os agentes?',
                  answer: 'Após criar sua conta, você terá acesso imediato aos 7 agentes. Cada um tem instruções específicas e exemplos de uso.'
                },
                {
                  question: 'Posso integrar com outras ferramentas?',
                  answer: 'Sim! Oferecemos integrações com as principais ferramentas de marketing e CRM. Veja a lista completa na documentação.'
                },
                {
                  question: 'Como funciona a cobrança?',
                  answer: 'Cobramos mensalmente via cartão de crédito. Você pode cancelar a qualquer momento sem multas.'
                },
                {
                  question: 'Os dados ficam seguros?',
                  answer: 'Absolutamente. Todos os dados são criptografados e seguimos as melhores práticas de segurança da indústria.'
                },
                {
                  question: 'Há limite de uso dos agentes?',
                  answer: 'Não há limite de uso dos agentes. Você pode gerar quantos resultados precisar dentro do seu plano.'
                }
              ].map((faq, index) => (
                <Card key={index}>
                  <CardHeader>
                    <CardTitle className="text-lg">{faq.question}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-foreground/70">{faq.answer}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Contact Form */}
          <div>
            <h2 className="text-2xl font-space-grotesk font-bold mb-8">
              Entre em Contato
            </h2>
            
            <Card>
              <CardHeader>
                <CardTitle>Envie sua mensagem</CardTitle>
                <CardDescription>
                  Responderemos em até 24 horas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Nome
                      </label>
                      <Input placeholder="Seu nome" />
                    </div>
                    <div>
                      <label className="text-sm font-medium mb-2 block">
                        Email
                      </label>
                      <Input type="email" placeholder="<EMAIL>" />
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Assunto
                    </label>
                    <Input placeholder="Como podemos ajudar?" />
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium mb-2 block">
                      Mensagem
                    </label>
                    <Textarea 
                      placeholder="Descreva sua dúvida ou problema..."
                      className="min-h-[120px]"
                    />
                  </div>
                  
                  <Button className="w-full">
                    <Icon name="mail" size={16} className="mr-2" />
                    Enviar mensagem
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Info */}
            <div className="mt-8 space-y-4">
              <h3 className="text-lg font-space-grotesk font-semibold">
                Outras formas de contato
              </h3>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <Icon name="mail" className="text-primary" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center space-x-3">
                  <Icon name="info" className="text-primary" />
                  <span>Respondemos em até 24 horas</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Resources Section */}
        <div className="mt-20">
          <h2 className="text-2xl font-space-grotesk font-bold text-center mb-12">
            Recursos Úteis
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto">
            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Icon name="file-text" className="text-primary" size={24} />
                </div>
                <CardTitle>Documentação</CardTitle>
                <CardDescription>
                  Guias completos para usar cada agente
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Ver documentação
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Icon name="users" className="text-primary" size={24} />
                </div>
                <CardTitle>Comunidade</CardTitle>
                <CardDescription>
                  Conecte-se com outros usuários
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Entrar na comunidade
                </Button>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <Icon name="info" className="text-primary" size={24} />
                </div>
                <CardTitle>Status</CardTitle>
                <CardDescription>
                  Verifique o status dos serviços
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button variant="outline" className="w-full">
                  Ver status
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  )
}
