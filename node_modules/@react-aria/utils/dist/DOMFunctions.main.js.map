{"mappings": ";;;;;;;;;;;AAAA,kIAAkI;;;AAQ3H,SAAS,0CACd,IAA6B,EAC7B,SAAkC;IAElC,IAAI,CAAC,CAAA,GAAA,kCAAQ,KACX,OAAO,aAAa,OAAO,KAAK,QAAQ,CAAC,aAAa;IAGxD,IAAI,CAAC,QAAQ,CAAC,WACZ,OAAO;IAGT,IAAI,cAAqD;IAEzD,MAAO,gBAAgB,KAAM;QAC3B,IAAI,gBAAgB,MAClB,OAAO;QAGT,IAAI,AAAC,YAAgC,OAAO,KAAK,UAC/C,AAAC,YAAgC,YAAY,EAC7C,qBAAqB;QACrB,cAAc,AAAC,YAAgC,YAAY,CAAE,UAAU;aAClE,IAAI,CAAA,GAAA,sCAAW,EAAE,cACtB,4BAA4B;QAC5B,cAAc,YAAY,IAAI;aAE9B,cAAc,YAAY,UAAU;IAExC;IAEA,OAAO;AACT;AAKO,MAAM,4CAAmB,CAAC,MAAgB,QAAQ;QAOvD;IANA,IAAI,CAAC,CAAA,GAAA,kCAAQ,KACX,OAAO,IAAI,aAAa;IAE1B,IAAI,gBAAgC,IAAI,aAAa;IAErD,MAAO,iBAAiB,gBAAgB,mBACxC,4BAAA,cAAc,UAAU,cAAxB,gDAAA,0BAA0B,aAAa,EACrC,gBAAgB,cAAc,UAAU,CAAC,aAAa;IAGxD,OAAO;AACT;AAKO,SAAS,0CAAgC,KAAQ;IACtD,IAAI,CAAA,GAAA,kCAAQ,OAAO,AAAC,MAAM,MAAM,CAAiB,UAAU,EAAE;QAC3D,IAAI,MAAM,YAAY,EACpB,OAAO,MAAM,YAAY,EAAE,CAAC,EAAE;IAElC;IACA,OAAO,MAAM,MAAM;AACrB", "sources": ["packages/@react-aria/utils/src/shadowdom/DOMFunctions.ts"], "sourcesContent": ["// Source: https://github.com/microsoft/tabster/blob/a89fc5d7e332d48f68d03b1ca6e344489d1c3898/src/Shadowdomize/DOMFunctions.ts#L16\n\nimport {isShadowRoot} from '../domHelpers';\nimport {shadowDOM} from '@react-stately/flags';\n\n/**\n * ShadowDOM safe version of Node.contains.\n */\nexport function nodeContains(\n  node: Node | null | undefined,\n  otherNode: Node | null | undefined\n): boolean {\n  if (!shadowDOM()) {\n    return otherNode && node ? node.contains(otherNode) : false;\n  }\n\n  if (!node || !otherNode) {\n    return false;\n  }\n\n  let currentNode: HTMLElement | Node | null | undefined = otherNode;\n\n  while (currentNode !== null) {\n    if (currentNode === node) {\n      return true;\n    }\n\n    if ((currentNode as HTMLSlotElement).tagName === 'SLOT' &&\n      (currentNode as HTMLSlotElement).assignedSlot) {\n      // Element is slotted\n      currentNode = (currentNode as HTMLSlotElement).assignedSlot!.parentNode;\n    } else if (isShadowRoot(currentNode)) {\n      // Element is in shadow root\n      currentNode = currentNode.host;\n    } else {\n      currentNode = currentNode.parentNode;\n    }\n  }\n\n  return false;\n}\n\n/**\n * ShadowDOM safe version of document.activeElement.\n */\nexport const getActiveElement = (doc: Document = document): Element | null => {\n  if (!shadowDOM()) {\n    return doc.activeElement;\n  }\n  let activeElement: Element | null = doc.activeElement;\n\n  while (activeElement && 'shadowRoot' in activeElement &&\n  activeElement.shadowRoot?.activeElement) {\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n\n  return activeElement;\n};\n\n/**\n * ShadowDOM safe version of event.target.\n */\nexport function getEventTarget<T extends Event>(event: T): Element {\n  if (shadowDOM() && (event.target as HTMLElement).shadowRoot) {\n    if (event.composedPath) {\n      return event.composedPath()[0] as Element;\n    }\n  }\n  return event.target as Element;\n}\n"], "names": [], "version": 3, "file": "DOMFunctions.main.js.map"}