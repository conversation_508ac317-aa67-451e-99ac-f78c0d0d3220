{"name": "@stripe/stripe-js", "version": "7.9.0", "description": "Stripe.js loading utility", "repository": "github:stripe/stripe-js", "main": "lib/index.js", "module": "lib/index.mjs", "jsnext:main": "lib/index.mjs", "types": "lib/index.d.ts", "typings": "lib/index.d.ts", "releaseCandidate": false, "scripts": {"test": "yarn lint && yarn test:unit && yarn test:package-types && yarn test:types && yarn typecheck", "test:unit": "jest", "test:package-types": "attw --pack . --entrypoints ./lib ./pure", "test:types": "zx ./tests/types/scripts/test.mjs", "lint": "eslint '{src,types}/**/*.{ts,js}' && yarn prettier-check", "typecheck": "tsc", "copy-types": "./scripts/copy-types", "build": "yarn clean && yarn rollup -c && yarn copy-types", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "echo \"\nPlease use ./scripts/publish instead\n\" && exit 1", "prettier": "prettier './**/*.{js,ts,md,html,css}' --write", "prettier-check": "prettier './**/*.{js,ts,md,html,css}' --check"}, "keywords": ["Stripe", "Stripe.js", "Elements"], "author": "Stripe (https://www.stripe.com)", "license": "MIT", "homepage": "https://stripe.com/docs/js", "files": ["dist", "lib", "pure", "src"], "engines": {"node": ">=12.16"}, "devDependencies": {"@arethetypeswrong/cli": "^0.15.3", "@babel/core": "^7.7.2", "@babel/preset-env": "^7.7.1", "@rollup/plugin-replace": "^2.3.1", "@types/jest": "^24.0.25", "@typescript-eslint/eslint-plugin": "^4.15.2", "@typescript-eslint/parser": "^4.15.2", "babel-eslint": "^10.0.3", "babel-jest": "^24.9.0", "conditional-type-checks": "^1.0.5", "eslint": "^6.8.0", "eslint-config-prettier": "^6.8.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^22.6.3", "eslint-plugin-prettier": "^3.1.1", "jest": "^24.9.0", "prettier": "^1.19.1", "rimraf": "^2.6.2", "rollup": "^2.79.2", "rollup-plugin-babel": "^4.3.3", "rollup-plugin-typescript2": "^0.25.3", "ts-jest": "^24.3.0", "typescript": "~4.8.0", "zx": "^4.2.0"}}