{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'BRL') {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: Date | string) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function slugify(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n\nexport function truncate(text: string, length: number) {\n  if (text.length <= length) return text\n  return text.slice(0, length) + '...'\n}\n\nexport function generateId() {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function isValidEmail(email: string) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function getInitials(name: string) {\n  return name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-2xl text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-background hover:bg-primary/90',\n        destructive: 'bg-destructive text-background hover:bg-destructive/90',\n        outline: 'border border-border bg-transparent hover:bg-muted hover:text-foreground',\n        secondary: 'bg-secondary text-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-muted hover:text-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-xl px-3',\n        lg: 'h-11 rounded-2xl px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,2RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,mNAAgB,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cn } from '@/lib/utils'\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-2xl border border-border bg-secondary/50 text-foreground shadow-card',\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = 'Card'\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('flex flex-col space-y-1.5 p-6', className)} {...props} />\n))\nCardHeader.displayName = 'CardHeader'\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight font-space-grotesk',\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = 'CardTitle'\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-foreground/70', className)}\n    {...props}\n  />\n))\nCardDescription.displayName = 'CardDescription'\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n))\nCardContent.displayName = 'CardContent'\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n))\nCardFooter.displayName = 'CardFooter'\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,mNAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,gFACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,mNAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EACX,yEACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,mNAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,mNAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,IAAA,yHAAE,EAAC,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,mNAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 236, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/layout/header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,wQAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/layout/header.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Header() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,SAAS,IAAA,wQAAuB,EACzC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 264, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/layout/footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport function Footer() {\n  return (\n    <footer className=\"border-t border-border bg-secondary/30\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          {/* Brand */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n                <span className=\"text-background font-bold text-sm\">B</span>\n              </div>\n              <span className=\"font-space-grotesk font-bold text-xl\">Backroom</span>\n            </div>\n            <p className=\"text-foreground/70 text-sm\">\n              Seu time invisível de elite. 7 IAs estratégicas que tiram você do modo reativo.\n            </p>\n          </div>\n\n          {/* Product */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-space-grotesk font-semibold\">Produto</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/agentes\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Agentes\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/pricing\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Preços\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/dashboard\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Dashboard\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Support */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-space-grotesk font-semibold\">Suporte</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/suporte\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Central de Ajuda\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contato\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Contato\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/status\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Status\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Legal */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-space-grotesk font-semibold\">Legal</h3>\n            <ul className=\"space-y-2 text-sm\">\n              <li>\n                <Link href=\"/termos\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Termos de Uso\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacidade\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Privacidade\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/cookies\" className=\"text-foreground/70 hover:text-foreground transition-colors\">\n                  Cookies\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n\n        <div className=\"mt-12 pt-8 border-t border-border flex flex-col md:flex-row justify-between items-center\">\n          <p className=\"text-foreground/70 text-sm\">\n            © 2024 Backroom. Todos os direitos reservados.\n          </p>\n          <p className=\"text-foreground/70 text-sm mt-4 md:mt-0\">\n            Feito com ❤️ para agências que querem escalar.\n          </p>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEO,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;sDAEtD,8OAAC;4CAAK,WAAU;sDAAuC;;;;;;;;;;;;8CAEzD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAM5C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;sDAI/F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;sDAI/F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAa,WAAU;0DAA6D;;;;;;;;;;;;;;;;;;;;;;;sCAQrG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;sDAI/F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;sDAI/F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA6D;;;;;;;;;;;;;;;;;;;;;;;sCAQlG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAU,WAAU;0DAA6D;;;;;;;;;;;sDAI9F,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAe,WAAU;0DAA6D;;;;;;;;;;;sDAInG,8OAAC;sDACC,cAAA,8OAAC,uKAAI;gDAAC,MAAK;gDAAW,WAAU;0DAA6D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrG,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;AAOjE", "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/app/page.tsx"], "sourcesContent": ["import { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Head<PERSON> } from '@/components/layout/header'\nimport { Footer } from '@/components/layout/footer'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background text-foreground\">\n      <Header />\n      {/* Hero Section */}\n      <section className=\"container mx-auto px-4 py-20 text-center\">\n        <div className=\"max-w-4xl mx-auto\">\n          <h1 className=\"text-5xl md:text-7xl font-space-grotesk font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent\">\n            Backroom.\n          </h1>\n          <h2 className=\"text-2xl md:text-3xl font-space-grotesk font-semibold mb-4 text-foreground/90\">\n            Seu time invisível de elite.\n          </h2>\n          <p className=\"text-lg md:text-xl text-foreground/70 mb-8 max-w-2xl mx-auto\">\n            7 IAs estratégicas que tiram você do modo reativo.\n          </p>\n\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button size=\"lg\" className=\"text-lg px-8 py-6\">\n              Começar teste grátis\n            </Button>\n            <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8 py-6\">\n              Ver agentes\n            </Button>\n          </div>\n        </div>\n      </section>\n\n      {/* Agents Preview */}\n      <section className=\"container mx-auto px-4 py-20\">\n        <div className=\"text-center mb-12\">\n          <h3 className=\"text-3xl font-space-grotesk font-semibold mb-4\">\n            Seus 7 Agentes Estratégicos\n          </h3>\n          <p className=\"text-foreground/70 max-w-2xl mx-auto\">\n            Cada agente é especializado em uma etapa crítica do processo de agência.\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto\">\n          {[\n            { name: 'Intake Radar', desc: 'Transforma pedidos soltos em sumário acionável.' },\n            { name: 'Briefing Builder', desc: 'Gera o briefing v1 completo, pronto para revisão.' },\n            { name: 'Perguntas Críticas', desc: 'Lista o que precisa voltar para o cliente, por prioridade.' },\n            { name: 'Persona & Segmentos', desc: 'Cria 2–3 personas acionáveis com canais e mensagens.' },\n            { name: 'Jornada Mapper', desc: 'Mapa por estágio: o que dizer, onde e como medir.' },\n            { name: 'Media Planner', desc: 'Divide orçamento e cronograma com justificativas e \"e se\".' },\n            { name: 'Performance Analyst', desc: 'KPIs, UTMs e relatório claro: o que, por quê, próximo passo.' },\n          ].map((agent, index) => (\n            <Card key={index} className=\"hover:shadow-lg transition-shadow\">\n              <CardHeader>\n                <CardTitle className=\"text-lg\">{agent.name}</CardTitle>\n                <CardDescription>{agent.desc}</CardDescription>\n              </CardHeader>\n              <CardContent>\n                <Button variant=\"outline\" className=\"w-full\">\n                  Abrir agente\n                </Button>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"container mx-auto px-4 py-20 text-center\">\n        <div className=\"max-w-2xl mx-auto\">\n          <h3 className=\"text-3xl font-space-grotesk font-semibold mb-4\">\n            Pronto para sair do modo reativo?\n          </h3>\n          <p className=\"text-foreground/70 mb-8\">\n            Teste grátis por 7 dias. Sem compromisso.\n          </p>\n          <Button size=\"lg\" className=\"text-lg px-8 py-6\">\n            Começar teste grátis\n          </Button>\n        </div>\n      </section>\n\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gJAAM;;;;;0BAEP,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAyI;;;;;;sCAGvJ,8OAAC;4BAAG,WAAU;sCAAgF;;;;;;sCAG9F,8OAAC;4BAAE,WAAU;sCAA+D;;;;;;sCAI5E,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4IAAM;oCAAC,MAAK;oCAAK,WAAU;8CAAoB;;;;;;8CAGhD,8OAAC,4IAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,WAAU;8CAAoB;;;;;;;;;;;;;;;;;;;;;;;0BAQxE,8OAAC;gBAAQ,WAAU;;kCACjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;;;;;;;kCAKtD,8OAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,MAAM;gCAAgB,MAAM;4BAAkD;4BAChF;gCAAE,MAAM;gCAAoB,MAAM;4BAAoD;4BACtF;gCAAE,MAAM;gCAAsB,MAAM;4BAA6D;4BACjG;gCAAE,MAAM;gCAAuB,MAAM;4BAAuD;4BAC5F;gCAAE,MAAM;gCAAkB,MAAM;4BAAoD;4BACpF;gCAAE,MAAM;gCAAiB,MAAM;4BAA6D;4BAC5F;gCAAE,MAAM;gCAAuB,MAAM;4BAA+D;yBACrG,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,8OAAC,wIAAI;gCAAa,WAAU;;kDAC1B,8OAAC,8IAAU;;0DACT,8OAAC,6IAAS;gDAAC,WAAU;0DAAW,MAAM,IAAI;;;;;;0DAC1C,8OAAC,mJAAe;0DAAE,MAAM,IAAI;;;;;;;;;;;;kDAE9B,8OAAC,+IAAW;kDACV,cAAA,8OAAC,4IAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAAS;;;;;;;;;;;;+BANtC;;;;;;;;;;;;;;;;0BAgBjB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAiD;;;;;;sCAG/D,8OAAC;4BAAE,WAAU;sCAA0B;;;;;;sCAGvC,8OAAC,4IAAM;4BAAC,MAAK;4BAAK,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAMpD,8OAAC,gJAAM;;;;;;;;;;;AAGb", "debugId": null}}]}