import Link from 'next/link'

export function Footer() {
  return (
    <footer className="border-t border-border bg-secondary/30">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                <span className="text-background font-bold text-sm">B</span>
              </div>
              <span className="font-space-grotesk font-bold text-xl">Backroom</span>
            </div>
            <p className="text-foreground/70 text-sm">
              Seu time invisível de elite. 7 IAs estratégicas que tiram você do modo reativo.
            </p>
          </div>

          {/* Product */}
          <div className="space-y-4">
            <h3 className="font-space-grotesk font-semibold">Produto</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/agentes" className="text-foreground/70 hover:text-foreground transition-colors">
                  Agentes
                </Link>
              </li>
              <li>
                <Link href="/pricing" className="text-foreground/70 hover:text-foreground transition-colors">
                  Preços
                </Link>
              </li>
              <li>
                <Link href="/dashboard" className="text-foreground/70 hover:text-foreground transition-colors">
                  Dashboard
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div className="space-y-4">
            <h3 className="font-space-grotesk font-semibold">Suporte</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/suporte" className="text-foreground/70 hover:text-foreground transition-colors">
                  Central de Ajuda
                </Link>
              </li>
              <li>
                <Link href="/contato" className="text-foreground/70 hover:text-foreground transition-colors">
                  Contato
                </Link>
              </li>
              <li>
                <Link href="/status" className="text-foreground/70 hover:text-foreground transition-colors">
                  Status
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal */}
          <div className="space-y-4">
            <h3 className="font-space-grotesk font-semibold">Legal</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/termos" className="text-foreground/70 hover:text-foreground transition-colors">
                  Termos de Uso
                </Link>
              </li>
              <li>
                <Link href="/privacidade" className="text-foreground/70 hover:text-foreground transition-colors">
                  Privacidade
                </Link>
              </li>
              <li>
                <Link href="/cookies" className="text-foreground/70 hover:text-foreground transition-colors">
                  Cookies
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-12 pt-8 border-t border-border flex flex-col md:flex-row justify-between items-center">
          <p className="text-foreground/70 text-sm">
            © 2024 Backroom. Todos os direitos reservados.
          </p>
          <p className="text-foreground/70 text-sm mt-4 md:mt-0">
            Feito com ❤️ para agências que querem escalar.
          </p>
        </div>
      </div>
    </footer>
  )
}
