import Link from 'next/link'
import { LoginForm } from '@/components/auth/login-form'

export default function LoginPage() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2">
            <div className="w-10 h-10 bg-primary rounded-2xl flex items-center justify-center">
              <span className="text-background font-bold">B</span>
            </div>
            <span className="font-space-grotesk font-bold text-2xl">Backroom</span>
          </Link>
        </div>

        <LoginForm />

        {/* Back to home */}
        <div className="text-center mt-8">
          <Link 
            href="/" 
            className="text-sm text-foreground/70 hover:text-foreground transition-colors"
          >
            ← Voltar para o site
          </Link>
        </div>
      </div>
    </div>
  )
}
