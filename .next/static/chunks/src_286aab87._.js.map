{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatCurrency(amount: number, currency = 'BRL') {\n  return new Intl.NumberFormat('pt-BR', {\n    style: 'currency',\n    currency,\n  }).format(amount)\n}\n\nexport function formatDate(date: Date | string) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: Date | string) {\n  return new Intl.DateTimeFormat('pt-BR', {\n    day: '2-digit',\n    month: '2-digit',\n    year: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function slugify(text: string) {\n  return text\n    .toLowerCase()\n    .normalize('NFD')\n    .replace(/[\\u0300-\\u036f]/g, '')\n    .replace(/[^a-z0-9\\s-]/g, '')\n    .replace(/\\s+/g, '-')\n    .replace(/-+/g, '-')\n    .trim()\n}\n\nexport function truncate(text: string, length: number) {\n  if (text.length <= length) return text\n  return text.slice(0, length) + '...'\n}\n\nexport function generateId() {\n  return Math.random().toString(36).substring(2) + Date.now().toString(36)\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function isValidEmail(email: string) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function getInitials(name: string) {\n  return name\n    .split(' ')\n    .map(word => word[0])\n    .join('')\n    .toUpperCase()\n    .slice(0, 2)\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,IAAA,yKAAO,EAAC,IAAA,gJAAI,EAAC;AACtB;AAEO,SAAS,eAAe,MAAc;QAAE,WAAA,iEAAW;IACxD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;IACR,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,KAAK;QACL,OAAO;QACP,MAAM;QACN,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAC5B,OAAO,CAAC,iBAAiB,IACzB,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,OAAO,KACf,IAAI;AACT;AAEO,SAAS,SAAS,IAAY,EAAE,MAAc;IACnD,IAAI,KAAK,MAAM,IAAI,QAAQ,OAAO;IAClC,OAAO,KAAK,KAAK,CAAC,GAAG,UAAU;AACjC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,KAAK,KAAK,GAAG,GAAG,QAAQ,CAAC;AACvE;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;AACd", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { cva, type VariantProps } from 'class-variance-authority'\nimport { cn } from '@/lib/utils'\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center whitespace-nowrap rounded-2xl text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n  {\n    variants: {\n      variant: {\n        default: 'bg-primary text-background hover:bg-primary/90',\n        destructive: 'bg-destructive text-background hover:bg-destructive/90',\n        outline: 'border border-border bg-transparent hover:bg-muted hover:text-foreground',\n        secondary: 'bg-secondary text-foreground hover:bg-secondary/80',\n        ghost: 'hover:bg-muted hover:text-foreground',\n        link: 'text-primary underline-offset-4 hover:underline',\n      },\n      size: {\n        default: 'h-10 px-4 py-2',\n        sm: 'h-9 rounded-xl px-3',\n        lg: 'h-11 rounded-2xl px-8',\n        icon: 'h-10 w-10',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,IAAA,0KAAG,EACxB,2RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,2KAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,qBACE,6LAAC;QACC,WAAW,IAAA,4HAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/ui/icon.tsx"], "sourcesContent": ["import { \n  Radar,\n  FileText,\n  HelpCircle,\n  Users,\n  Map,\n  Calendar,\n  BarChart,\n  Upload,\n  Link,\n  Copy,\n  Download,\n  Mail,\n  Search,\n  Plus,\n  Settings,\n  User,\n  LogOut,\n  Menu,\n  X,\n  ChevronDown,\n  ChevronRight,\n  Check,\n  AlertCircle,\n  Info,\n  type LucideIcon\n} from 'lucide-react'\n\nexport type IconName = \n  | 'radar'\n  | 'file-text'\n  | 'help-circle'\n  | 'users'\n  | 'map'\n  | 'calendar'\n  | 'bar-chart'\n  | 'upload'\n  | 'link'\n  | 'copy'\n  | 'download'\n  | 'mail'\n  | 'search'\n  | 'plus'\n  | 'settings'\n  | 'user'\n  | 'log-out'\n  | 'menu'\n  | 'x'\n  | 'chevron-down'\n  | 'chevron-right'\n  | 'check'\n  | 'alert-circle'\n  | 'info'\n\nconst iconMap: Record<IconName, LucideIcon> = {\n  'radar': Radar,\n  'file-text': FileText,\n  'help-circle': HelpCircle,\n  'users': Users,\n  'map': Map,\n  'calendar': Calendar,\n  'bar-chart': BarChart,\n  'upload': Upload,\n  'link': Link,\n  'copy': Copy,\n  'download': Download,\n  'mail': Mail,\n  'search': Search,\n  'plus': Plus,\n  'settings': Settings,\n  'user': User,\n  'log-out': LogOut,\n  'menu': Menu,\n  'x': X,\n  'chevron-down': ChevronDown,\n  'chevron-right': ChevronRight,\n  'check': Check,\n  'alert-circle': AlertCircle,\n  'info': Info,\n}\n\ninterface IconProps {\n  name: IconName\n  className?: string\n  size?: number\n}\n\nexport const Icon = ({ name, className, size = 20 }: IconProps) => {\n  const IconComponent = iconMap[name]\n  \n  if (!IconComponent) {\n    console.warn(`Icon \"${name}\" not found`)\n    return null\n  }\n\n  return <IconComponent className={className} size={size} />\n}\n\nexport { iconMap }\n"], "names": [], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAsDA,MAAM,UAAwC;IAC5C,SAAS,gNAAK;IACd,aAAa,6NAAQ;IACrB,eAAe,+OAAU;IACzB,SAAS,gNAAK;IACd,OAAO,0MAAG;IACV,YAAY,yNAAQ;IACpB,aAAa,4PAAQ;IACrB,UAAU,mNAAM;IAChB,QAAQ,6MAAI;IACZ,QAAQ,6MAAI;IACZ,YAAY,yNAAQ;IACpB,QAAQ,6MAAI;IACZ,UAAU,mNAAM;IAChB,QAAQ,6MAAI;IACZ,YAAY,yNAAQ;IACpB,QAAQ,6MAAI;IACZ,WAAW,uNAAM;IACjB,QAAQ,6MAAI;IACZ,KAAK,oMAAC;IACN,gBAAgB,sOAAW;IAC3B,iBAAiB,yOAAY;IAC7B,SAAS,gNAAK;IACd,gBAAgB,sOAAW;IAC3B,QAAQ,6MAAI;AACd;AAQO,MAAM,OAAO;QAAC,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,EAAa;IAC5D,MAAM,gBAAgB,OAAO,CAAC,KAAK;IAEnC,IAAI,CAAC,eAAe;QAClB,QAAQ,IAAI,CAAC,AAAC,SAAa,OAAL,MAAK;QAC3B,OAAO;IACT;IAEA,qBAAO,6LAAC;QAAc,WAAW;QAAW,MAAM;;;;;;AACpD;KATa", "debugId": null}}, {"offset": {"line": 244, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/projects/ad-agent7/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/button'\nimport { Icon } from '@/components/ui/icon'\nimport { useState } from 'react'\n\nexport function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4 h-16 flex items-center justify-between\">\n        {/* Logo */}\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n            <span className=\"text-background font-bold text-sm\">B</span>\n          </div>\n          <span className=\"font-space-grotesk font-bold text-xl\">Backroom</span>\n        </Link>\n\n        {/* Desktop Navigation */}\n        <nav className=\"hidden md:flex items-center space-x-8\">\n          <Link \n            href=\"/agentes\" \n            className=\"text-foreground/70 hover:text-foreground transition-colors\"\n          >\n            Agentes\n          </Link>\n          <Link \n            href=\"/pricing\" \n            className=\"text-foreground/70 hover:text-foreground transition-colors\"\n          >\n            Preços\n          </Link>\n          <Link \n            href=\"/suporte\" \n            className=\"text-foreground/70 hover:text-foreground transition-colors\"\n          >\n            Suporte\n          </Link>\n        </nav>\n\n        {/* Desktop CTA */}\n        <div className=\"hidden md:flex items-center space-x-4\">\n          <Link href=\"/login\">\n            <Button variant=\"ghost\">Entrar</Button>\n          </Link>\n          <Link href=\"/signup\">\n            <Button>Começar teste grátis</Button>\n          </Link>\n        </div>\n\n        {/* Mobile Menu Button */}\n        <Button\n          variant=\"ghost\"\n          size=\"icon\"\n          className=\"md:hidden\"\n          onClick={() => setIsMenuOpen(!isMenuOpen)}\n        >\n          <Icon name={isMenuOpen ? 'x' : 'menu'} />\n        </Button>\n      </div>\n\n      {/* Mobile Menu */}\n      {isMenuOpen && (\n        <div className=\"md:hidden border-t border-border bg-background\">\n          <nav className=\"container mx-auto px-4 py-4 space-y-4\">\n            <Link \n              href=\"/agentes\" \n              className=\"block text-foreground/70 hover:text-foreground transition-colors\"\n              onClick={() => setIsMenuOpen(false)}\n            >\n              Agentes\n            </Link>\n            <Link \n              href=\"/pricing\" \n              className=\"block text-foreground/70 hover:text-foreground transition-colors\"\n              onClick={() => setIsMenuOpen(false)}\n            >\n              Preços\n            </Link>\n            <Link \n              href=\"/suporte\" \n              className=\"block text-foreground/70 hover:text-foreground transition-colors\"\n              onClick={() => setIsMenuOpen(false)}\n            >\n              Suporte\n            </Link>\n            <div className=\"pt-4 space-y-2\">\n              <Link href=\"/login\" className=\"block\">\n                <Button variant=\"ghost\" className=\"w-full justify-start\">\n                  Entrar\n                </Button>\n              </Link>\n              <Link href=\"/signup\" className=\"block\">\n                <Button className=\"w-full\">Começar teste grátis</Button>\n              </Link>\n            </div>\n          </nav>\n        </div>\n      )}\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,qBACE,6LAAC;QAAO,WAAU;;0BAChB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,0KAAI;wBAAC,MAAK;wBAAI,WAAU;;0CACvB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAoC;;;;;;;;;;;0CAEtD,6LAAC;gCAAK,WAAU;0CAAuC;;;;;;;;;;;;kCAIzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCA<PERSON>,<PERSON>AK;gCACL,WAAU;0CACX;;;;;;0CAGD,6LAAC,0KAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;kCAMH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,0KAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,+IAAM;oCAAC,SAAQ;8CAAQ;;;;;;;;;;;0CAE1B,6LAAC,0KAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,+IAAM;8CAAC;;;;;;;;;;;;;;;;;kCAKZ,6LAAC,+IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS,IAAM,cAAc,CAAC;kCAE9B,cAAA,6LAAC,2IAAI;4BAAC,MAAM,aAAa,MAAM;;;;;;;;;;;;;;;;;YAKlC,4BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,cAAc;sCAC9B;;;;;;sCAGD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0KAAI;oCAAC,MAAK;oCAAS,WAAU;8CAC5B,cAAA,6LAAC,+IAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAAuB;;;;;;;;;;;8CAI3D,6LAAC,0KAAI;oCAAC,MAAK;oCAAU,WAAU;8CAC7B,cAAA,6LAAC,+IAAM;wCAAC,WAAU;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ3C;GAjGgB;KAAA", "debugId": null}}]}